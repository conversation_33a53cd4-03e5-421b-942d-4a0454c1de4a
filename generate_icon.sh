#!/bin/bash

sips -z 16 16     icon.png --out MyIcon.iconset/icon_16x16.png
sips -z 32 32     icon.png --out MyIcon.iconset/<EMAIL>
sips -z 32 32     icon.png --out MyIcon.iconset/icon_32x32.png
sips -z 64 64     icon.png --out MyIcon.iconset/<EMAIL>
sips -z 128 128   icon.png --out MyIcon.iconset/icon_128x128.png
sips -z 256 256   icon.png --out MyIcon.iconset/<EMAIL>
sips -z 256 256   icon.png --out MyIcon.iconset/icon_256x256.png
sips -z 512 512   icon.png --out MyIcon.iconset/<EMAIL>
sips -z 512 512   icon.png --out MyIcon.iconset/icon_512x512.png
cp icon.png MyIcon.iconset/<EMAIL>
iconutil -c icns MyIcon.iconset -o Storyboard.icns
mv Storyboard.icns assets/Storyboard.icns

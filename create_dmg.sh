#!/bin/bash

# Script to create a .dmg file with the app and readme
# Usage: ./create_dmg.sh

set -e  # Exit on any error

# Configuration
APP_NAME="AI Storyboard Generator"
DMG_NAME="AI-Storyboard-Generator"
VERSION="1.0.0"
BACKGROUND_COLOR="#2c3e50"
WINDOW_WIDTH=600
WINDOW_HEIGHT=400

# Paths
DIST_DIR="dist"
APP_PATH="$DIST_DIR/$APP_NAME-darwin-x64/$APP_NAME.app"
DMG_TEMP_DIR="dmg_temp"
DMG_FINAL="$DMG_NAME-$VERSION.dmg"

echo "🚀 Creating DMG for $APP_NAME v$VERSION"

# Check if the app exists
if [ ! -d "$APP_PATH" ]; then
    echo "❌ Error: App not found at $APP_PATH"
    echo "Please run ./build.sh first to build the app"
    exit 1
fi

# Clean up any previous DMG creation
echo "🧹 Cleaning up previous DMG files..."
rm -rf "$DMG_TEMP_DIR"
rm -f "$DMG_FINAL"
rm -f "${DMG_FINAL}.temp.dmg"

# Create temporary directory for DMG contents
echo "📁 Creating temporary DMG directory..."
mkdir -p "$DMG_TEMP_DIR"

# Copy the app to the temporary directory
echo "📦 Copying app to DMG directory..."
cp -R "$APP_PATH" "$DMG_TEMP_DIR/"

# Create a README file
echo "📝 Creating README file..."
cat > "$DMG_TEMP_DIR/README.txt" << EOF
AI Storyboard Generator v$VERSION
================================

Thank you for downloading AI Storyboard Generator!

INSTALLATION:
1. Drag the "AI Storyboard Generator" app to your Applications folder
2. Launch the app from your Applications folder
3. If you see a security warning, go to System Preferences > Security & Privacy and click "Open Anyway"

SYSTEM REQUIREMENTS:
- macOS 10.13 or later
- Internet connection for AI features

FEATURES:
- AI-powered storyboard generation
- Customizable brand styling
- Export to PowerPoint format
- Intuitive drag-and-drop interface

SUPPORT:
For support and updates, please visit our website or contact support.

LEGAL:
This software is provided "as is" without warranty of any kind.
© $(date +%Y) Publicis Sapient. All rights reserved.

Enjoy creating amazing storyboards!
EOF

# Create Applications symlink for easy installation
echo "🔗 Creating Applications symlink..."
ln -s /Applications "$DMG_TEMP_DIR/Applications"

# Calculate DMG size (app size + some padding)
echo "📏 Calculating DMG size..."
APP_SIZE=$(du -sm "$APP_PATH" | cut -f1)
DMG_SIZE=$((APP_SIZE + 50))  # Add 50MB padding

# Create the DMG
echo "💿 Creating DMG file..."
hdiutil create -srcfolder "$DMG_TEMP_DIR" -volname "$APP_NAME" -fs HFS+ \
    -fsargs "-c c=64,a=16,e=16" -format UDRW -size ${DMG_SIZE}m "${DMG_FINAL}.temp.dmg"

# Mount the DMG for customization
echo "🔧 Mounting DMG for customization..."
DEVICE=$(hdiutil attach -readwrite -noverify "${DMG_FINAL}.temp.dmg" | \
    egrep '^/dev/' | sed 1q | awk '{print $1}')
MOUNT_POINT="/Volumes/$APP_NAME"

# Wait for mount to complete
sleep 2

# Customize the DMG appearance using AppleScript
echo "🎨 Customizing DMG appearance..."
osascript << EOF
tell application "Finder"
    tell disk "$APP_NAME"
        open
        set current view of container window to icon view
        set toolbar visible of container window to false
        set statusbar visible of container window to false
        set the bounds of container window to {100, 100, $((100 + WINDOW_WIDTH)), $((100 + WINDOW_HEIGHT))}
        set viewOptions to the icon view options of container window
        set arrangement of viewOptions to not arranged
        set icon size of viewOptions to 128
        set background color of viewOptions to {11776, 15872, 20480}

        -- Position the app icon
        set position of item "$APP_NAME.app" of container window to {150, 200}

        -- Position the Applications symlink
        set position of item "Applications" of container window to {450, 200}

        -- Position the README file
        set position of item "README.txt" of container window to {300, 300}

        -- Update and close
        update without registering applications
        delay 2
        close
    end tell
end tell
EOF

# Unmount the DMG
echo "📤 Unmounting DMG..."
hdiutil detach "$DEVICE"

# Convert to final compressed DMG
echo "🗜️ Converting to final compressed DMG..."
hdiutil convert "${DMG_FINAL}.temp.dmg" -format UDZO -imagekey zlib-level=9 -o "$DMG_FINAL"

# Clean up
echo "🧹 Cleaning up temporary files..."
rm -f "${DMG_FINAL}.temp.dmg"
rm -rf "$DMG_TEMP_DIR"

# Get final file size
FINAL_SIZE=$(du -h "$DMG_FINAL" | cut -f1)

echo "✅ DMG creation complete!"
echo "📁 File: $DMG_FINAL"
echo "📊 Size: $FINAL_SIZE"
echo ""
echo "🎉 Your DMG is ready for distribution!"
echo "Users can now drag the app to Applications and read the README for instructions."

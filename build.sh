#!/bin/bash

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf dist/

# Build with optimized settings
echo "🚀 Building optimized app..."
npx electron-packager . 'AI Storyboard Generator' \
  --platform=darwin \
  --arch=x64 \
  --out=dist \
  --icon=assets/Storyboard.icns \
  --overwrite \
  --prune=true \
  --ignore="^/(src|docs|test|spec|\.git|\.github|\.vscode|\.idea|coverage|\.nyc_output|\.DS_Store|Thumbs\.db|desktop\.ini|npm-debug\.log|\.npm|\.eslintrc|\.babelrc|\.editorconfig|\.gitignore|README\.md|CHANGELOG\.md|LICENSE|\.travis\.yml|\.appveyor\.yml|webpack\.config\.js|rollup\.config\.js|gulpfile\.js|Gruntfile\.js|tsconfig\.json|tslint\.json|\.prettierrc|jest\.config\.js|karma\.conf\.js|protractor\.conf\.js|e2e|cypress|\.storybook|stories|docs|examples|demo|benchmark|perf|\.circleci|\.gitlab-ci\.yml|azure-pipelines\.yml|Jenkinsfile|Dockerfile|docker-compose\.yml|\.dockerignore|Vagrantfile|\.vagrant|Makefile|CMakeLists\.txt|configure|autogen\.sh|\.autotools|\.m4|\.in$|\.am$|\.ac$|build\.sh|generate_icon\.sh|MyIcon\.iconset)" \
  --ignore="node_modules/.bin" \
  --asar \
  --asar-unpack="*.{node,dll}" \
  --no-deref-symlinks

# npx electron-packager . 'AI Storyboard Generator' \
#   --platform=darwin \
#   --arch=x64 \
#   --out=dist \
#   --icon=assets/Storyboard.icns \
#   --overwrite


echo "✅ Build complete! App size optimized."